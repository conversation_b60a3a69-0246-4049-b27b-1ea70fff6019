from pydantic import BaseModel, Field, SecretStr, SerializationInfo, field_serializer
from pydantic.json import pydantic_encoder

from ii_agent.utils.constants import WorkSpaceMode


class SandboxConfig(BaseModel):
    """Configuration for the sandbox."""

    mode: WorkSpaceMode = Field(default=WorkSpaceMode.LOCAL)
    template_id: str | None = Field(default=None)
    sandbox_api_key: SecretStr | None = Field(default=None)
    service_port: int = Field(default=17300)

    @field_serializer("sandbox_api_key")
    def api_key_serializer(self, api_key: SecretStr | None, info: SerializationInfo):
        """Custom serializer for API keys.

        To serialize the API key instead of ********, set expose_secrets to True in the serialization context.
        """
        if api_key is None:
            return None

        context = info.context
        if context and context.get("expose_secrets", False):
            return api_key.get_secret_value()

        return pydantic_encoder(api_key)
